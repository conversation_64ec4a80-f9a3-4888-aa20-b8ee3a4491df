// {
//   "compileOnSave": false,
//   "compilerOptions": {
//     "baseUrl": "./",
//     "outDir": "./dist/out-tsc",
//     "sourceMap": true,
//     "declaration": false,
//     "moduleResolution": "node",
//     "emitDecoratorMetadata": true,
//     "experimentalDecorators": true,
//     "target": "es5",
//     "typeRoots": [
//       "node_modules/@types"
//     ],
//     "lib": [
//       "es2016",
//       "dom"
//     ],
//     "skipLibCheck": true,
//     "skipDefaultLibCheck": true,
//     "noEmitOnError": false,
//     "strict": false,
//     "suppressImplicitAnyIndexErrors": true,
//     "noImplicitAny": false,
//     "noImplicitReturns": false,
//     "noImplicitThis": false,
//     "noStrictGenericChecks": true,
//     "suppressExcessPropertyErrors": true,
//     "paths": {
//       "exceljs": [
//         "node_modules/exceljs/dist/es5/exceljs.browser"
//       ]
//     }
//   }
// }


{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "target": "es6",  // Updated from es5
    "typeRoots": [
      "node_modules/@types"
    ],
    "lib": [
      "es2016",
      "dom"
    ],
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "noEmitOnError": true,  // Changed from false
    "strict": false,
    "paths": {
      "exceljs": [
        "node_modules/exceljs/dist/es5/exceljs.browser"
      ],
      "@/*": ["src/*"]  // Added for better module resolution
    }
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "src/assets/vendors"
  ]
}