{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es5", "typeRoots": ["node_modules/@types", "./"], "lib": ["es2016", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "noEmitOnError": false, "forceConsistentCasingInFileNames": false, "allowSyntheticDefaultImports": true, "suppressImplicitAnyIndexErrors": true, "noImplicitAny": false, "strict": false, "noImplicitReturns": false, "noImplicitThis": false, "noStrictGenericChecks": true, "suppressExcessPropertyErrors": true, "isolatedModules": false, "maxNodeModuleJsDepth": 0, "allowJs": true, "checkJs": false, "paths": {"exceljs": ["node_modules/exceljs/dist/es5/exceljs.browser"]}}, "exclude": ["node_modules/**/*", "src/assets/vendors/**/*", "src/assets/lib/**/*"], "types": ["node"], "typeRoots": ["node_modules/@types", "src/typings.d.ts"]}