// Custom type declarations to override problematic third-party libraries
// This file overrides the problematic type definitions to work with TypeScript 2.6.2

declare module 'autolinker' {
  export = Autolinker;
  class Autolinker {
    static link(text: string, options?: any): string;
    constructor(options?: any);
    link(text: string): string;
  }
  namespace Autolinker {
    interface Match {
      getMatchedText(): string;
      getAnchorHref(): string;
      getAnchorText(): string;
    }
  }
}

declare module 'dexie' {
  export = Dexie;
  class Dexie {
    constructor(name: string);
    version(version: number): any;
    stores(schema: any): any;
    open(): Promise<any>;
    close(): void;
    delete(): Promise<void>;
    table(name: string): any;
  }
  namespace Dexie {
    interface Table {
      add(item: any): Promise<any>;
      put(item: any): Promise<any>;
      get(key: any): Promise<any>;
      delete(key: any): Promise<void>;
      clear(): Promise<void>;
      toArray(): Promise<any[]>;
    }
  }
}

declare module 'elevio' {
  export function load(accountId: string): void;
  export function identify(user: any): void;
  export function setUser(user: any): void;
  export function setSettings(settings: any): void;
  export function setTranslations(translations: any): void;
  export function open(): void;
  export function close(): void;
  export function setAccount(accountId: string): void;
  export function boot(): void;
}

declare module 'graphql-tag' {
  export = gql;
  function gql(literals: TemplateStringsArray, ...placeholders: any[]): any;
  function gql(query: string): any;
}

declare module 'moment-timezone' {
  import * as moment from 'moment';

  interface MomentTimezone extends moment.Moment {
    tz(): string;
    tz(timezone: string): MomentTimezone;
    format(): string;
    format(format: string): string;
  }

  interface MomentTimezoneStatic extends moment.MomentStatic {
    tz(timezone: string): MomentTimezone;
    tz(date: any, timezone: string): MomentTimezone;
    tz(date: any, format: string, timezone: string): MomentTimezone;
    tz(date: any, format: string, locale: string, timezone: string): MomentTimezone;
  }

  const momentTimezone: MomentTimezoneStatic;
  export = momentTimezone;
}

declare module 'socket.io-client' {
  export = io;
  function io(uri?: string, options?: any): SocketIOClient.Socket;

  namespace SocketIOClient {
    interface Socket {
      on(event: string, fn: Function): Socket;
      once(event: string, fn: Function): Socket;
      off(event: string, fn?: Function): Socket;
      emit(event: string, ...args: any[]): Socket;
      disconnect(): Socket;
      connect(): Socket;
      connected: boolean;
      id: string;
      io: any;
    }

    interface Manager {
      connect(fn?: Function): Socket;
      open(fn?: Function): Socket;
      socket(nsp: string, opts?: any): Socket;
    }
  }
}

// Fix for missing type definition files
declare module 'app' {
  const app: any;
  export = app;
}

declare module 'assets' {
  const assets: any;
  export = assets;
}

declare module 'environments' {
  const environments: any;
  export = environments;
}

declare module 'mocks' {
  const mocks: any;
  export = mocks;
}

// Ignore vendor JavaScript files that shouldn't be type-checked
declare module '*/autosize.js' {
  const autosize: any;
  export = autosize;
}

declare module '*/gulpfile-wrap-template.js' {
  const gulpfile: any;
  export = gulpfile;
}

// Generic declaration for all vendor JS files
declare module 'src/assets/vendors/**/*' {
  const vendor: any;
  export = vendor;
}

// ts-optchain replacement - simple implementation
declare module 'ts-optchain' {
  export function oc<T>(obj: T): any;
}
