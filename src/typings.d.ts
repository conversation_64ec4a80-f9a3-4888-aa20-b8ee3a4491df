// Custom type declarations to override problematic third-party libraries

declare module 'autolinker' {
  export = Autolinker;
  class Autolinker {
    static link(text: string, options?: any): string;
    constructor(options?: any);
    link(text: string): string;
  }
}

declare module 'dexie' {
  export = Dexie;
  class Dexie {
    constructor(name: string);
    version(version: number): any;
    stores(schema: any): any;
    open(): Promise<any>;
    close(): void;
    delete(): Promise<void>;
  }
}

declare module 'elevio' {
  export function load(accountId: string): void;
  export function identify(user: any): void;
  export function setUser(user: any): void;
  export function setSettings(settings: any): void;
  export function setTranslations(translations: any): void;
  export function open(): void;
  export function close(): void;
}

declare module 'graphql-tag' {
  export = gql;
  function gql(literals: TemplateStringsArray, ...placeholders: any[]): any;
}

declare module 'moment-timezone' {
  import * as moment from 'moment';
  export = moment;
}

declare module 'socket.io-client' {
  export = io;
  function io(uri?: string, options?: any): any;
}
